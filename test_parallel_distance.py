#!/usr/bin/env python3
"""
测试多边形最小平行边距离计算功能
"""

import numpy as np
import cv2
import itertools
import math

def are_lines_parallel(line1, line2, tolerance=1e-3):
    """判断两条线是否平行"""
    v1 = (line1[1][0] - line1[0][0], line1[1][1] - line1[0][1])
    v2 = (line2[1][0] - line2[0][0], line2[1][1] - line2[0][1])
    
    len1 = math.sqrt(v1[0]**2 + v1[1]**2)
    len2 = math.sqrt(v2[0]**2 + v2[1]**2)
    
    if len1 < tolerance or len2 < tolerance:
        return False
    
    v1_norm = (v1[0]/len1, v1[1]/len1)
    v2_norm = (v2[0]/len2, v2[1]/len2)
    
    cross_product = abs(v1_norm[0] * v2_norm[1] - v1_norm[1] * v2_norm[0])
    return cross_product < tolerance

def distance_between_parallel_lines(line1, line2):
    """计算两条平行线之间的垂直距离"""
    x1, y1 = line1[0]
    x2, y2 = line1[1]
    
    A = y2 - y1
    B = x1 - x2
    C = x2 * y1 - x1 * y2
    
    x3, y3 = line2[0]
    distance = abs(A * x3 + B * y3 + C) / math.sqrt(A**2 + B**2)
    return distance

def is_line_inside_polygon(line, polygon_vertices):
    """判断线段是否在多边形内部"""
    try:
        polygon_array = np.array(polygon_vertices, dtype=np.float32).reshape(-1, 1, 2)
        
        point1 = (float(line[0][0]), float(line[0][1]))
        point2 = (float(line[1][0]), float(line[1][1]))
        
        point1_inside = cv2.pointPolygonTest(polygon_array, point1, False) >= 0
        point2_inside = cv2.pointPolygonTest(polygon_array, point2, False) >= 0
        
        if not (point1_inside and point2_inside):
            return False
        
        mid_x = (point1[0] + point2[0]) / 2
        mid_y = (point1[1] + point2[1]) / 2
        mid_point = (float(mid_x), float(mid_y))
        mid_point_inside = cv2.pointPolygonTest(polygon_array, mid_point, False) >= 0
        
        return mid_point_inside
        
    except Exception as e:
        print(f"检查线段是否在多边形内时出错: {e}")
        return False

def calculate_min_parallel_distance(polygon_vertices):
    """计算多边形角点间最小平行边距离"""
    num_vertices = len(polygon_vertices)
    
    if num_vertices < 4:
        print(f"多边形顶点数不足4个({num_vertices})，无法计算平行边距离")
        return None
    
    print(f"开始计算多边形平行边距离，顶点数: {num_vertices}")
    
    min_distance = float('inf')
    valid_parallel_pairs = 0
    total_combinations = 0
    
    try:
        for four_points in itertools.combinations(polygon_vertices, 4):
            lines = list(itertools.combinations(four_points, 2))
            
            for line_pair in itertools.combinations(lines, 2):
                total_combinations += 1
                line1, line2 = line_pair
                
                if are_lines_parallel(line1, line2):
                    print(f"  发现平行线对: {line1} 和 {line2}")
                    
                    connecting_lines = [
                        (line1[0], line2[0]),
                        (line1[0], line2[1]),
                        (line1[1], line2[0]),
                        (line1[1], line2[1])
                    ]
                    
                    all_lines_inside = True
                    for i, connecting_line in enumerate(connecting_lines):
                        if not is_line_inside_polygon(connecting_line, polygon_vertices):
                            print(f"    连接线{i+1} {connecting_line} 不在多边形内")
                            all_lines_inside = False
                            break
                    
                    if all_lines_inside:
                        distance = distance_between_parallel_lines(line1, line2)
                        if distance < min_distance:
                            min_distance = distance
                        valid_parallel_pairs += 1
                        print(f"  找到有效平行边对，距离: {distance:.2f}像素")
                    else:
                        print(f"    平行线对被跳过：连接线不全在多边形内")

    except Exception as e:
        print(f"计算多边形平行边距离时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

    print(f"计算完成: 总组合数={total_combinations}, 有效平行边对数={valid_parallel_pairs}")
    
    if min_distance == float('inf'):
        print("未找到符合条件的平行边")
        return None
    
    print(f"最小平行边距离: {min_distance:.2f}像素")
    return min_distance

# 测试用例
if __name__ == "__main__":
    # 测试用例1：正方形
    print("=== 测试用例1：正方形 ===")
    square_vertices = [(0, 0), (100, 0), (100, 100), (0, 100)]
    result = calculate_min_parallel_distance(square_vertices)
    print(f"正方形最小平行边距离: {result}")
    
    # 测试用例2：矩形
    print("\n=== 测试用例2：矩形 ===")
    rectangle_vertices = [(0, 0), (150, 0), (150, 80), (0, 80)]
    result = calculate_min_parallel_distance(rectangle_vertices)
    print(f"矩形最小平行边距离: {result}")
    
    # 测试用例3：六边形
    print("\n=== 测试用例3：六边形 ===")
    hexagon_vertices = [
        (50, 0), (100, 25), (100, 75), (50, 100), (0, 75), (0, 25)
    ]
    result = calculate_min_parallel_distance(hexagon_vertices)
    print(f"六边形最小平行边距离: {result}")
